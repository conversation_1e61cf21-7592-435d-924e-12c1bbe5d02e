import HomeBanner from '@/components/home/<USER>';
import ArticlesPage from './media/page';
import NewsletterInput from '@/components/home/<USER>';
import { getTrends, getCompanies } from './utils/data';
import Trends from '@/components/home/<USER>';
import CompanySlider from '@/components/home/<USER>';
import signature from '@/assets/images/signature.svg';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import Image from 'next/image';
import Link from 'next/link';

import { Metadata } from "next";

export async function generateMetadata({ params }: { params: { lang: string } }): Promise<Metadata> {
  const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || '';
  const langSlug = params.lang;

  return {
    title: `All Eyes On Me - job, emploi et recrutement au Luxembourg`,
    description: `Découvrez la culture des entreprises qui recrutent au Luxembourg. Trouvez l'offre d'emploi qui vous inspire. Boostez votre carrière et votre salaire.`,
    alternates: {
        canonical: `${baseUrl}/${langSlug}`,
        languages: {
          fr: `${baseUrl}/fr`,
          en: `${baseUrl}/en`,
          'x-default': `${baseUrl}/fr`,
        }
    },
  };
}

export default async function HomePage({
  searchParams,
}: {
  searchParams?: { trend: string };
}) {

  const trendData = await getTrends();
  let trends = trendData?.trends ?? [];
  trends = trends.sort((a, b) => a.code.localeCompare(b.code));
  const companiesData = await getCompanies(searchParams?.trend ? searchParams?.trend : trends[0]?.code);
  const companies = companiesData?.companies;

  return (
    <div className='flex flex-col w-full overflow-hidden'>
      <section id='banner' className='flex w-full'>
        <HomeBanner />
      </section>
      <section id='recruting' className='flex w-full py-16'>
        <div className='flex flex-col w-full bg-white items-center xslg:container mx-auto gap-[24px]'>
          <h2 className='text-primary leading-[38px] text-[32px] text-center md:text-start font-geo231Bt'>
            Les entreprises qui recrutent
          </h2>
          <div className='flex flex-col w-full items-center'>
            <Trends trends={trends} selectedTrend={searchParams?.trend} />
            <CompanySlider companies={JSON.parse(JSON.stringify(companies))} />
          </div>
          <h3 className='text-secondary text-[32px] leading-[38px] font-geo231Bt text-center'>
            Trouvez votre future entreprise
          </h3>
          <div className='flex w-[310px]'>
            
            <Link
            href="/companies"
            className='bg-primary text-white rounded-lg p-2 space-x-2 w-full text-center font-bold font-arvo'
          >
            Explorez
          </Link>
          </div>
        </div>
      </section>
      <section id='recruting' className='flex w-full py-[40px] bg-neutral'>
        <div className='flex flex-col w-full items-center container mx-auto'>
          <h3 className='text-secondary text-[32px] font-geo231Bt leading-[39px] text-center'>
            Pourquoi le Luxembourg?
          </h3>
          <p className='text-primary text-lg font-inter mt-6 mb-8 lg:w-1/2 text-center leading-[22px]'>
            Venez découvrir le Luxembourg, un pays offrant un cadre de vie
            exceptionnel et des perspectives de carrière dans divers secteurs en
            plein essor.
          </p>
          <div className='flex'>
            <Link href='https://alleyesonme.jobs/media/pourquoi-travailler-au-luxembourg-est-une-opportunite-unique'>
              <button
                type='submit'
                className='bg-secondary text-white rounded-lg p-2 space-x-2 w-full py-[8px] px-[47px]'
              >
                <span className='text-sm font-bold font-arvo'>
                  Voir plus
                </span>
              </button>
            </Link>
          </div>
        </div>
      </section>
      <section id='articles'>
        <ArticlesPage showTopBar={false} showPagination={false} />
      </section>
      <section id='newsletter' className='py-[72px]'>
        <div className='flex flex-col lg:flex-row w-2/3 lg:w-1/2 items-center px-[24px] lg:px-0  mx-auto gap-[38px]'>
          <div className='flex flex-col gap-6'>
            <div className='relative'>
              <Image
                  src={signature}
                  alt='Arrow'
                  height={104}
                  className='pb-4 px-4 absolute -top-[10px] -left-[100px]'
                />
                <h4 className='text-primary text-lg font-geo231Bt absolute'>
                  Je m&apos;abonne
                </h4>
              </div>
              <p className='text-primary text-lg font-inter pt-6'>
                Boostez votre carrière ! Inscrivez-vous à notre newsletter pour
                recevoir des offres d&apos;emploi, des conseils de carrière, et
                rester informé des tendances du marché au Luxembourg.
              </p>
            </div>
          <NewsletterInput />
        </div>
      </section>
    </div>
  );
}
