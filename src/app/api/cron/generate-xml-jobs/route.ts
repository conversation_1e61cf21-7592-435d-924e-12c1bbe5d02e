import { put } from '@vercel/blob';
import {NextResponse} from 'next/server';
import {XML_OPEN_JOBS} from "@/types/xml";
import {MongoClient} from "mongodb";
import {JobStatus} from "@/models/enums";
import {<PERSON><PERSON><PERSON>} from "@/models/Job";
import {ValueByLang} from "@/utils/languages";

export async function GET(req: Request) {
	try {
		//////////////////////////////
		// Authentication
		const authHeader = req.headers.get('Authorization');
		if (authHeader !== `Bearer ${process.env.CRON_SECRET}`) {
			console.error("invalid or missing header: " + authHeader);
			return NextResponse.json({error: 'Unauthorized'}, {status: 401});
		}


		//////////////////////////////
		// Database
		console.log('connecting to database ...');
		const client = new MongoClient(process.env.MONGODB_URI!);
		await client.connect();
		const db = client.db(process.env.MONGODB_DB_NAME);


		//////////////////////////////
		// Jobs
		console.log('fetching jobs ...');
		const jobs = await db.collection('jobs').find({ status: JobStatus.Open }).toArray() as IJob[];


		//////////////////////////////
		// XML File
		console.log('generating xml ...');
		const xmlContent = generateXMLContent(jobs);


		//////////////////////////////
		// Storage
		console.log('creating file ...');
		const blob = await put(XML_OPEN_JOBS, xmlContent, {
			access: 'public',
			contentType: 'application/xml',
			allowOverwrite: true,
		});

		console.log('blob.pathname');
		return NextResponse.json({
			success: true,
			pathname: blob.pathname, // Store pathname for later retrieval
			message: 'XML updated successfully'
		});
	} catch (error: any) {
		console.error('execution failed:', error);
		return NextResponse.json({ok: false, error: error.message}, {status: 500});
	} finally {
		console.log('done');
	}
}
function generateXMLContent(jobs: IJob[]) {
	let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
	xml += '<jobs>\n';

	for (const job of jobs) {
		xml += `  <job id="${job._id.toString("hex")}">\n`;
		xml += xmlField('link', `https://alleyesonme.jobs/jobs/${job.slug}?utm_source=jooble&utm_medium=aggregator&utm_campaign=job_feed`);
		xml += xmlField('name', ValueByLang("fr", job.title));
		xml += xmlField('region', job.location?.city || job.location?.country || "Luxembourg");
		xml += xmlField('description', ValueByLang("fr", job.descriptions?.description));
		xml += xmlField('pubdate', formatDate(job.createdAt));
		xml += xmlField('updated', formatDate(job.updatedAt));

		if (job.salary?.fixedValue) {
			xml += xmlField('salary', job.salary?.fixedValue);
		}

		if (job.companyName) {
			xml += xmlField('company', job.companyName);
		}

		xml += xmlField('expire', formatDate(job.closingDate));
	}

	return 	xml + '</jobs>';
}

function xmlField(name: string, value: string) {
	return `  <${name}><![CDATA[${ value.replaceAll("]]>", ']]&gt;') }]></${name}>\n`;
}

function formatDate(date?: Date) : string  {
	if (!date) date = new Date();

	const pad = (num: number) => num.toString().padStart(2, '0');
	return `${pad(date.getDate())}-${pad(date.getMonth() + 1)}-${date.getFullYear()}`;
}