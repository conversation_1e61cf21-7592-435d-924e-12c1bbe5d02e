'use client';

import React, { useEffect, useRef, useState } from 'react';
import CompanyCard from '../Companies/CompanyCard';
import { CompanyResponse } from '@/services/CompaniesService';

import Slider from 'react-slick';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faChevronLeft,
  faChevronRight,
} from '@fortawesome/free-solid-svg-icons';

function SampleNextArrow(props: any) {
  const { className, style, onClick, hidden } = props;
  return (
    !hidden && (
      <FontAwesomeIcon
        icon={faChevronRight}
        className={className}
        style={{ ...style, display: 'block', color: '#FF4C00' }}
        onClick={onClick}
      />
    )
  );
}

function SamplePrevArrow(props: any) {
  const { className, style, onClick, hidden } = props;
  return (
    !hidden && (
      <FontAwesomeIcon
        icon={faChevronLeft}
        className={className}
        style={{ ...style, display: 'block', color: '#FF4C00' }}
        onClick={onClick}
      />
    )
  );
}

const throttle = (func: (...args: any[]) => void, limit: number) => {
  let inThrottle: boolean;
  return function (this: any, ...args: any[]) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
};

const calculateArrowVisibility = (
  currentSlide: number,
  companiesLength: number,
  slidesToShow: number
) => {
  const isPrevHidden = currentSlide === 0;
  const isNextHidden =
    companiesLength <= slidesToShow || currentSlide >= companiesLength - slidesToShow;
  return { isPrevHidden, isNextHidden };
};

const CompanySlider = ({ companies }: { companies: CompanyResponse[] }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const sliderRef = useRef<Slider | null>(null);

  useEffect(() => {
    setCurrentSlide(0); 
  }, [companies]);

  const onWheelHandler = throttle((e: React.WheelEvent<HTMLDivElement>) => {
    if (!sliderRef.current) return;

    const threshold = 5;

    if (e.deltaX > threshold) {
      sliderRef.current.slickNext();
    } else if (e.deltaX < -threshold) {
      sliderRef.current.slickPrev();
    }
  }, 500);

  const slidesToShow = 3; 
  const { isPrevHidden, isNextHidden } = calculateArrowVisibility(
    currentSlide,
    companies.length,
    slidesToShow
  );


  const settings = {
    dots: companies.length > slidesToShow,
    infinite: false,
    swipe: false,
    speed: 500,
    slidesToShow: slidesToShow,
    slidesToScroll: 1,
    centerMode: false,
    nextArrow: <SampleNextArrow hidden={isNextHidden} />,
    prevArrow: <SamplePrevArrow hidden={isPrevHidden} />,
    responsive: [
      {
        breakpoint: 1280,
        swipe: false,
        settings: {
          slidesToShow: slidesToShow - 1,
          dots: companies.length > slidesToShow - 1,
          nextArrow: (
            <SampleNextArrow
              hidden={
                calculateArrowVisibility(currentSlide, companies.length, slidesToShow - 1)
                  .isNextHidden
              }
            />
          ),
          prevArrow: (
            <SamplePrevArrow
              hidden={
                calculateArrowVisibility(currentSlide, companies.length, slidesToShow - 1)
                  .isPrevHidden
              }
            />
          ),
        },
      },
      {
        breakpoint: 826,
        settings: {
          slidesToShow: slidesToShow - 2, 
          dots: companies.length > slidesToShow - 2,
          nextArrow: (
            <SampleNextArrow
              hidden={
                calculateArrowVisibility(currentSlide, companies.length, slidesToShow - 2)
                  .isNextHidden
              }
            />
          ),
          prevArrow: (
            <SamplePrevArrow
              hidden={
                calculateArrowVisibility(currentSlide, companies.length, slidesToShow - 2)
                  .isPrevHidden
              }
            />
          ),
          arrows: false,
          swipe: true,
          centerMode: true, 
          className: "company-card-container",
        },
      },
    ],
    beforeChange: (oldIndex: number, newIndex: number) => {
      setCurrentSlide(newIndex);
    },
  };

  return (
    <div
      key={JSON.stringify(companies)}
      onWheel={onWheelHandler}
      className="flex flex-col slider-container w-[420px] xslg:w-[724px] xl:w-[1086px] my-6"
    >
      <Slider {...settings} ref={sliderRef} className="slick-slider-companies">
        {companies.map((company, index) => (
          <div
            key={`company_${index}`}
            className={` transition-all duration-300 ${
              index === currentSlide
                ? 'opacity-100 blur-0 scale-100 xslg:blur-0 xslg:scale-100'
                : 'opacity-100 blur-[2px] scale-90 xslg:blur-0 xslg:scale-100'
            }`}
          
          >
            <CompanyCard data={company} />
          </div>
        ))}
      </Slider>
      <style jsx global>{`
        .slick-list{
        @media (min-width: 826px) {
          overflow: hidden;
        }
          overflow: visible;
        }
          
        .slick-next {
          top: 50% !important;
          right: -16px !important;
        }
        .slick-prev{
          top: 50% !important;
          left: -16px !important;
        }
      `}</style>
    </div>
    
  );
};

export default CompanySlider;
